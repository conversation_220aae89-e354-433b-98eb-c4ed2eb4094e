﻿using System.ComponentModel.DataAnnotations.Schema;
using Data.Models.EntityModels;

namespace Data.Models
{
    public class Order : AuditableEntity<Guid>
    {
        [<PERSON><PERSON><PERSON>(nameof(User))]
        public string UserId { get; set; } = default!;

        public virtual ApplicationUser User { get; set; } = default!;

        [ForeignKey(nameof(Cart))]
        public Guid CartId { get; set; }

        public virtual Cart Cart { get; set; } = default!;

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Phone { get; set; }

        public string Address { get; set; }

        public int OrderStatusId { get; set; }

        public virtual OrderStatus OrderStatus { get; set; } = default!;
    }
}
