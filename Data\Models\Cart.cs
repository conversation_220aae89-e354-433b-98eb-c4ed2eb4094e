﻿using System.ComponentModel.DataAnnotations.Schema;
using Data.Models.EntityModels;

namespace Data.Models
{
    public class Cart : AuditableEntity<Guid>
    {
        [ForeignKey(nameof(User))]
        public string UserId { get; set; } = default!;

        public virtual ApplicationUser User { get; set; } = default!;

        public bool IsActive { get; set; } = true;

        public virtual List<CartItem> CartItems { get; set; } = new List<CartItem>();
    }
}
