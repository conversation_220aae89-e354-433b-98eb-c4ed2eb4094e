﻿using System.ComponentModel.DataAnnotations.Schema;
using Data.Models.EntityModels;

namespace Data.Models
{
    public class ProductSize : AuditableEntity<Guid>
    {
        [ForeignKey(nameof(Size))]
        public int? SizeId { get; set; }

        public virtual Size? Size { get; set; } = default!;

        [ForeignKey(nameof(Product))]
        public Guid? ProductId { get; set; }

        public virtual Product? Product { get; set; } = default!;

        public int AvailableQuantity { get; set; }
    }
}
