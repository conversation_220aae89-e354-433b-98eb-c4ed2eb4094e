﻿using System.ComponentModel.DataAnnotations.Schema;
using Data.Models.EntityModels;

namespace Data.Models
{
    public class CartItem : AuditableEntity<Guid>
    {
        [ForeignKey(nameof(Cart))]
        public Guid CartId { get; set; }

        public virtual Cart Cart { get; set; } = default!;

        [ForeignKey(nameof(Product))]
        public Guid ProductId { get; set; }

        public virtual Product Product { get; set; } = default!;

        [ForeignKey(nameof(ProductSize))]
        public Guid ProductSizeId { get; set; }

        public virtual ProductSize ProductSize { get; set; } = default!;

        public int Quantity { get; set; }
    }
}
