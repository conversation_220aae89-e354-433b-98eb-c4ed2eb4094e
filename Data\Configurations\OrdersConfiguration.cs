﻿using Data.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Net;

namespace Data.Configurations
{
    public class OrdersConfiguration : IEntityTypeConfiguration<Order>
    {
        public void Configure(EntityTypeBuilder<Order> builder)
        {
            builder .HasOne(o => o.Cart)
                    .WithMany()
                    .HasForeignKey(o => o.CartId)
                    .OnDelete(DeleteBehavior.NoAction);
        }
    }
}
